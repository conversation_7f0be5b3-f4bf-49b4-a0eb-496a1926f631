<template>
  <div class="guarantee">
    <!-- up -->
    <h1 class="titleTop">基本信息</h1>
    <div class="guaranteeSetTop">
      <avue-form
        v-model="form"
        ref="information"
        :option="option"
        :defaults.sync="defaults"
        @change="handleFormChange"
        @input="handleFormInput"
      >
        <template slot="wave">
          <div>
            <div style="text-align: center">~</div>
          </div>
        </template>
        <template slot="waveA">
          <div>
            <div style="text-align: center">~</div>
          </div>
        </template>
        <template slot="goodsLabelIds">
          <div>
            <el-select
              v-model="form.goodsLabelIds"
              placeholder="请选择产品组标签（最多3个）"
              multiple
              :multiple-limit="3"
              :disabled="lookRepaymentType"
            >
              <el-option
                v-for="item in option.column[13].dicData"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </div>
        </template>
      </avue-form>
    </div>
    <!-- down -->
    <div class="title-bottom">
      <h1 class="titleBottom">开通流程</h1>
      <avue-form ref="avueFS" :option="optionS" v-model="obj"> </avue-form>
    </div>
    <div class="title-bottom">
      <h1 class="titleBottom">常见问题</h1>
      <avue-form ref="avueFQ" :option="optionQ" v-model="objQ">
        <template slot-scope="{ row }" slot="answer">
          <div>
            <el-input
              v-model="row.answer"
              size="small"
              class="answer-input"
              placeholder="请输入答案"
              :disabled="elDisabl"
            ></el-input>
          </div>
        </template>
      </avue-form>
    </div>
    <h1 class="titleBottom">产品组背景</h1>
    <avue-form
      ref="avueFI"
      :option="optionI"
      v-model="objI"
      :upload-before="uploadBeforeImg"
      :upload-after="uploadAfterImg"
    >
    </avue-form>
  </div>
</template>

<script>
import { getListByType, getTagList } from '@/api/goods/pcontrol/pinformation'
// import { getGoodsDetail } from '@/api/goods/product-group'
import { detail } from '@/api/goods/manyCapitalProducts.js'
// import { getDictionary } from '@/api/goods/pcontrol/workflow/productConfirmation'

export default {
  props: ['look'],
  data() {
    return {
      id: this.$route.query.id,
      lookRepaymentType: false,
      elDisabl: false,
      isRedemption: false, // 是否为赎货类型
      form: {},
      obj: {},
      objQ: {},
      objI: {},
      option: {
        submitBtn: false,
        emptyBtn: false,
        labelWidth: 100,
        gutter: 80,
        column: [
          {
            label: '产品名称',
            prop: 'goodsName',
            span: 12,
            placeholder: '请输入融资产品名称',
            rules: [
              {
                required: true,
                message: '请填写融资产品名称',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '产品分类',
            prop: 'goodsTypeId',
            type: 'select',
            span: 12,
            placeholder: '请选择融资产品分类',
            dicData: [],
            rules: [
              {
                required: true,
                message: '请选择融资产品分类',
                trigger: 'change',
              },
            ],
          },
          {
            label: '借款金额',
            prop: 'loanAmount',
            span: 6,
            append: '万元',
            placeholder: false,
            className: 'loansleft1',
            rules: [
              {
                required: true,
                message: '请填写借款金额',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '',
            labelWidth: 0,
            span: 1,
            prop: 'wave',
            className: 'borrow-wave',
          },
          {
            label: '',
            prop: 'loanAmounted',
            span: 5,
            append: '万元',
            placeholder: false,
            labelWidth: 0,
            rules: [
              {
                required: true,
                message: '请填写借款金额',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '年利率',
            prop: 'annualInterestRateStart',
            span: 6,
            placeholder: false,
            className: 'loansleftguanlian',
            append: '%',
            rules: [
              {
                required: true,
                message: '请填写年利率',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '',
            labelWidth: 0,
            span: 1,
            prop: 'waveA',
          },
          {
            label: '',
            prop: 'annualInterestRateEnd',
            span: 4,
            placeholder: false,
            className: 'loansright',
            labelWidth: 0,
            append: '%',
            rules: [
              {
                required: true,
                message: '请填写年利率',
                trigger: 'blur',
              },
            ],
          },

          {
            label: '还款类型',
            span: 8,
            prop: 'repaymentType',
            placeholder: '请选择还款类型',
            type: 'select',
            dicUrl: '/api/blade-system/dict-biz/dictionary?code=goods_repayment_type',
            props: {
              label: 'dictValue',
              value: 'dictKey',
            },
            dataType: 'number',
            rules: [
              {
                required: true,
                message: '请选择还款类型',
                trigger: 'change',
              },
            ],
          },
          // {
          //   label: '计费方式',
          //   prop: 'billingMethod',
          //   type: 'tree',
          //   span: 12,
          //   multiple: true,
          //   parent: true,
          //   placeholder: '选择计费方式（多选）',
          //   // disabled: true,
          //   dicUrl: '/api/blade-system/dict-biz/dictionary?code=goods_billing_method',
          //   props: {
          //     label: 'dictValue',
          //     value: 'dictKey',
          //   },
          //   dataType: 'string',
          //   rules: [
          //     {
          //       required: true,
          //       message: '请选择计费方式',
          //       trigger: 'change',
          //     },
          //   ],
          // },
          {
            label: '借款期限',
            prop: 'loadTermUnit',
            type: 'select',
            span: 5,
            placeholder: '选择单位',
            className: 'loanstime',
            dicUrl: '/api/blade-system/dict-biz/dictionary?code=goods_load_term_unit',
            props: {
              label: 'dictValue',
              value: 'dictKey',
            },
            dataType: 'number',
            rules: [
              {
                required: true,
                message: '请选择单位',
                trigger: 'change',
              },
            ],
          },
          {
            label: '',
            prop: 'loadTerm',
            span: 4,
            placeholder: false,
            className: 'loansleft',
            labelWidth: 0,
            rules: [
              {
                required: true,
                message: '请填写期限',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '',
            labelWidth: 0,
            span: 1,
            prop: 'wave',
          },
          {
            label: '',
            prop: 'loadTermed',
            span: 4,
            placeholder: false,
            className: 'loansright',
            labelWidth: 0,
            rules: [
              {
                required: true,
                message: '请填写期限',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '产品标签',
            prop: 'goodsLabelIds',
            type: 'tree',
            span: 12,
            placeholder: '选择产品组标签（最多3个）',
            multiple: true,
            parent: true,
            dicData: [],
            rules: [
              {
                required: true,
                message: '请选择产品组标签',
                trigger: 'change',
              },
            ],
          },
          // 融资计算和融资调整字段将通过动态方法添加

          {
            label: '产品说明',
            prop: 'goodsExplain',
            type: 'textarea',
            span: 24,
            placeholder: '请输入产品组说明',
            minRows: 4,
            maxlength: 200,
            showWordLimit: true,
            rules: [
              {
                required: true,
                message: '请填写产品组说明',
                trigger: 'blur',
              },
            ],
          },
        ],
      },
      optionS: {
        labelWidth: 110,
        submitBtn: false,
        emptyBtn: false,
        column: [
          {
            // label: '子表单',
            labelWidth: 0,
            prop: 'dynamic',
            type: 'dynamic',
            span: 24,
            children: {
              addBtn: true,
              delBtn: true,
              align: 'center',
              headerAlign: 'center',
              // rowAdd: done => {
              //   done({
              //     procTheTitle: '',
              //   })
              // },
              // rowDel: (row, done) => {
              //   done()
              // },
              column: [
                {
                  width: 200,
                  label: '流程标题',
                  prop: 'procTheTitle',
                  placeholder: '请输入流程标题',
                  rules: [
                    {
                      required: true,
                      message: '请输入流程标题',
                      trigger: 'blur',
                    },
                  ],
                },
                {
                  width: 500,
                  label: '流程描述',
                  prop: 'theProcDescription',
                  type: 'input',
                  placeholder: '请输入流程描述',
                  rules: [
                    {
                      required: true,
                      message: '请输入流程描述',
                      trigger: 'blur',
                    },
                  ],
                },
                {
                  label: '流程图标',
                  prop: 'procIcon',
                  type: 'upload',
                  span: 24,
                  listType: 'picture-img',
                  // tip: '只能上传jpg/png文件，且不超过500kb',
                  propsHttp: {
                    res: 'data',
                  },
                  action: '/api/blade-resource/oss/endpoint/put-file-kv',
                  uploadBefore: (file, done, loading) => {
                    this.uploadBeforeIcon(file, done, loading)
                  },
                  rules: [
                    {
                      required: true,
                      message: '请上传流程图标',
                      trigger: 'change',
                    },
                  ],
                },
                {
                  label: '排序',
                  prop: 'sort',
                  placeholder: false,
                  rules: [
                    {
                      required: true,
                      message: '请输入流程排序',
                      trigger: 'blur',
                    },
                  ],
                },
              ],
            },
          },
        ],
      },
      optionQ: {
        labelWidth: 110,
        submitBtn: false,
        emptyBtn: false,
        column: [
          {
            // label: '子表单',
            labelWidth: 0,
            prop: 'dynamicQ',
            type: 'dynamic',
            span: 24,
            children: {
              addBtn: true,
              delBtn: true,
              align: 'center',
              headerAlign: 'center',
              // rowAdd: done => {
              //   done({
              //     issue: '',
              //   })
              // },
              // rowDel: (row, done) => {
              //   done()
              // },
              column: [
                {
                  width: 300,
                  label: '问题',
                  prop: 'issue',
                  placeholder: '请输入问题',
                  rules: [
                    {
                      required: true,
                      message: '请输入问题',
                      trigger: 'blur',
                    },
                  ],
                },
                {
                  width: 600,
                  label: '答案',
                  prop: 'answer',
                  placeholder: '请输入答案',
                  headerAlign: 'left',
                  rules: [
                    {
                      required: true,
                      message: '请输入答案',
                      trigger: 'blur',
                    },
                  ],
                },
                {
                  label: '排序',
                  prop: 'sortQ',
                  placeholder: false,
                  rules: [
                    {
                      required: true,
                      message: '请输入排序',
                      trigger: 'blur',
                    },
                  ],
                },
              ],
            },
          },
        ],
      },
      optionI: {
        labelWidth: 110,
        submitBtn: false,
        emptyBtn: false,
        column: [
          {
            label: false,
            labelWidth: 0,
            disabled: false,
            prop: 'backgroundImg',
            type: 'upload',
            span: 8,
            listType: 'picture-img',
            tip: '仅支持格式：jpg、png，大小不超过2M',
            accept: 'image/png, image/jpeg, image/jpg',
            // loadText: '附件上传中，请稍等',
            propsHttp: {
              res: 'data',
            },
            action: '/api/blade-resource/oss/endpoint/put-file-kv',
            rules: [
              {
                required: true,
                message: '请上传产品背景图片',
                trigger: 'change',
              },
            ],
          },
        ],
      },
    }
  },
  computed: {
    // 可以在这里添加其他计算属性
  },
  watch: {
    'form.repaymentType'(val) {
      // 还款类型选分期就只能以期为单位
      if (this.lookRepaymentType) return
      if (val) {
        this.defaults.loadTermUnit.disabled = true
        this.form.loadTermUnit = val == 1 ? 2 : 1
      }
    },
    // 监听融资计算的变化，动态更新表单配置
    'form.needCalculate': {
      handler(newVal, oldVal) {
        // 如果融资计算从"开"变为"关"，则同时将融资调整设为"关"
        if (oldVal === 1 && newVal === 0) {
          this.form.needAdjust = 0
        }
        this.$nextTick(() => {
          this.updateFormColumns()
        })
      },
      immediate: false,
    },
    // 监听isRedemption的变化，动态更新表单配置
    isRedemption(val) {
      this.$nextTick(() => {
        this.updateFormColumns()
      })
    },
  },
  created() {
    this.onLoadStart() // 加载对应选择框选项

    // 初始化表单列配置
    this.$nextTick(() => {
      this.updateFormColumns()
    })

    if (this.id) {
      if (this.look) {
        this.lookRepaymentType = true
      }
      this.getGoodsDetailed()
    }
  },
  mounted() {
    // 确保表单列配置正确初始化
    this.updateFormColumns()

    const set = setInterval(() => {
      if (this.option.column && this.look) {
        for (const item of this.option.column) {
          item.disabled = true
        }
        for (const item of this.optionS.column[0].children.column) {
          item.disabled = true
        }
        for (const item of this.optionQ.column[0].children.column) {
          item.disabled = true
        }
        this.optionS.column[0].children.addBtn = false
        this.optionS.column[0].children.delBtn = false
        this.optionQ.column[0].children.addBtn = false
        this.optionQ.column[0].children.delBtn = false
        this.elDisabl = true
        this.optionI.column[0].disabled = true
        clearInterval(set)
      }
    }, 100)
  },
  methods: {
    // 处理表单字段变化
    handleFormChange(value, column) {
      if (column && column.prop === 'needCalculate') {
        // 如果融资计算变为"关"，则同时将融资调整设为"关"
        if (value === 0) {
          this.form.needAdjust = 0
        }
        // 当融资计算字段变化时，立即更新表单列配置
        this.$nextTick(() => {
          this.updateFormColumns()
        })
      }
    },

    // 处理表单输入事件
    handleFormInput(value) {
      // 检查 needCalculate 是否变化
      if (value.hasOwnProperty('needCalculate')) {
        this.$nextTick(() => {
          this.updateFormColumns()
        })
      }
    },

    // 更新表单列配置
    updateFormColumns() {
      // 找到融资计算和融资调整字段的索引
      const needCalculateIndex = this.option.column.findIndex(col => col.prop === 'needCalculate')
      const needAdjustIndex = this.option.column.findIndex(col => col.prop === 'needAdjust')

      // 移除现有的融资计算和融资调整字段（从后往前删除，避免索引变化）
      if (needAdjustIndex !== -1) {
        this.option.column.splice(needAdjustIndex, 1)
      }
      if (needCalculateIndex !== -1) {
        this.option.column.splice(needCalculateIndex, 1)
      }

      // 找到产品标签字段的索引，在其后插入新字段
      const labelIndex = this.option.column.findIndex(col => col.prop === 'goodsLabelIds')
      let insertIndex = labelIndex + 1

      // 根据条件添加融资计算字段（只有isRedemption为false时才显示）
      if (!this.isRedemption) {
        const calculateColumn = {
          label: '融资计算',
          prop: 'needCalculate',
          type: 'switch',
          span: 6,
          dicData: [
            {
              label: '关',
              value: 0,
            },
            {
              label: '开',
              value: 1,
            },
          ],
        }
        this.option.column.splice(insertIndex, 0, calculateColumn)
        insertIndex++
      }

      // 根据条件添加融资调整字段（只有融资计算为"开"时才显示）
      if (!this.isRedemption && this.form.needCalculate === 1) {
        const adjustColumn = {
          label: '融资方案调整',
          prop: 'needAdjust',
          type: 'switch',
          span: 6,
          dicData: [
            {
              label: '关',
              value: 0,
            },
            {
              label: '开',
              value: 1,
            },
          ],
        }
        this.option.column.splice(insertIndex, 0, adjustColumn)
      }
    },

    // 开通流程校验
    kaitongliuchengjiaoyanFun() {
      if (!this.obj.dynamic.length) {
        this.$message.warning('请完善产品信息-开通流程的数据')
        return
      }
      this.$refs.avueFS.validate((valid1, done1) => {
        if (!valid1) {
          // this.$message.warning('请完善产品信息-开通流程的必填数据')
          return
        }
        done1()
        this.changjianwentijiaoyanFun()
      })
    },
    // 常见问题校验
    changjianwentijiaoyanFun() {
      if (!this.objQ.dynamicQ.length) {
        this.$message.warning('请完善产品信息-常见问题的数据')
        return
      }
      this.$refs.avueFQ.validate((valid2, done2) => {
        if (!valid2) {
          // this.$message.warning('请完善产品信息-常见问题的必填数据')
          return
        }
        done2()
        this.chanpinbeijingjiaoyanFun()
      })
    },
    // 产品背景图片校验
    chanpinbeijingjiaoyanFun() {
      this.$refs.avueFI.validate((valid3, done3) => {
        if (!valid3) {
          // this.$message.warning('请完善产品信息-产品背景的上传')
          return
        }
        done3()
        this.formSubmit()
      })
    },
    handleSubmit(save) {
      if (!save) {
        this.$refs.information.validate((valid, done, msg) => {
          if (!valid) {
            let errMsg = Object.values(msg)[0].message
            if (!errMsg) {
              errMsg = Object.values(msg)[0][0].message
              if (!errMsg) {
                errMsg = '必填项未填'
              }
            }
            this.$message.warning(errMsg)
            return
          }
          done()
          // 开通流程校验
          this.kaitongliuchengjiaoyanFun()
        })
        this.formSubmit() // 这个校验有问题，没有true的状态
      } else {
        if (!this.form.goodsName) {
          return this.$message.warning('请先填写产品组名称再进行保存')
        }
        if (!this.form.goodsTypeId) {
          return this.$message.warning('请先选择产品组类型再进行保存')
        }
        this.formSubmit()
      }
    },
    // 新增
    formSubmit() {
      if (
        Number(this.form.loadTerm) &&
        Number(this.form.loadTermed) &&
        Number(this.form.loadTerm) >= Number(this.form.loadTermed)
      ) {
        this.$message.error('请确认借款期限范围是否正常')
        return
      }
      if (
        Number(this.form.loanAmount) &&
        Number(this.form.loanAmounted) &&
        Number(this.form.loanAmount) >= Number(this.form.loanAmounted)
      ) {
        this.$message.error('请确认借款金额范围是否正常')
        return
      }
      // if (this.form.goodsLabelIds && this.form.goodsLabelIds.length > 3) {
      //   this.$message.error('产品组标签最多只能选择3个')
      //   return
      // }
      this.$store.commit('SET_CPZ_VALID_CHUN_TYPE', ['one', true])

      const goodsQuestionsed = []
      const goodsOpeningProcessesed = []
      for (const item of this.objQ.dynamicQ) {
        // 处理常见问题
        goodsQuestionsed.push({
          answer: item.answer,
          name: item.issue,
          sort: item.sortQ,
        })
      }
      for (const item of this.obj.dynamic) {
        // 处理开通流程
        goodsOpeningProcessesed.push({
          description: item.theProcDescription,
          logo: item.procIcon,
          sort: item.sort,
          title: item.procTheTitle,
        })
      }
      const fromData = this.form
      let params = {
        annualInterestRateStart: fromData.annualInterestRateStart,
        annualInterestRateEnd: fromData.annualInterestRateEnd,
        labelIds: fromData.goodsLabelIds,
        goodsName: fromData.goodsName,
        goodsTypeId: fromData.goodsTypeId,
        loadTermUnit: fromData.loadTermUnit,
        needAdjust: fromData.needAdjust || 0,
        needCalculate: fromData.needCalculate || 0,
        repaymentType: fromData.repaymentType, // 还款类型
        // billingMethod: fromData.billingMethod ? fromData.billingMethod : '0',
        type: 99, // 类型
        background: this.objI.backgroundImg, // 背景图
        goodsExplain: fromData.goodsExplain, // 产品组说明
        goodsOpeningProcesses: goodsOpeningProcessesed, // 开通流程
        goodsQuestions: goodsQuestionsed, // 常见问题
        loanAmountStart: fromData.loanAmount,
        loanAmountEnd: fromData.loanAmounted,
        loadTermStart: fromData.loadTerm,
        loadTermEnd: fromData.loadTermed,
      }

      this.$store.commit('SET_FORM_PARAMS_DATAED', params)
    },
    // 查看
    getGoodsDetailed() {
      detail(this.id).then(res => {
        const resData = res.data
        if (resData.code == 200) {
          // setTimeout(() => {
          //   this.lookRepaymentType = false
          // }, 1000)
          const chlok = resData.data
          console.log(chlok)

          // 设置isRedemption值
          this.isRedemption = chlok.isRedemption || false
          this.form.goodsName = chlok.goodsName
          this.form.goodsTypeId = chlok.goodsTypeId
          this.form.loanAmount = chlok.loanAmountStart
          this.form.loanAmounted = chlok.loanAmountEnd
          this.form.needAdjust = chlok.needAdjust
          this.form.needCalculate = chlok.needCalculate
          if (Number(chlok.annualInterestRateStart) !== Number('0')) {
            this.form.annualInterestRateStart = chlok.annualInterestRateStart
          }
          if (Number(chlok.annualInterestRateEnd) !== Number('0')) {
            this.form.annualInterestRateEnd = chlok.annualInterestRateEnd
          }
          this.form.repaymentType = chlok.repaymentType
          this.form.loadTermUnit = chlok.loadTermUnit
          this.form.loadTerm = chlok.loadTermStart
          this.form.loadTermed = chlok.loadTermEnd
          this.form.goodsLabelIds = chlok.labelIds
          this.form.goodsExplain = chlok.goodsExplain

          for (const item of chlok.goodsOpeningProcesses) {
            this.obj['dynamic'].push({
              procTheTitle: item.title,
              theProcDescription: item.description,
              procIcon: item.logo,
              sort: item.sort,
            })
          }
          for (const item of chlok.goodsQuestions) {
            this.objQ['dynamicQ'].push({
              issue: item.name,
              answer: item.answer,
              sortQ: item.sort,
            })
          }
          this.obj['dynamic'] = chlok.goodsOpeningProcesses
          this.objQ['dynamicQ'] = chlok.goodsQuestions
          this.objI['backgroundImg'] = chlok.background

          // 数据加载完成后更新表单列配置
          this.$nextTick(() => {
            this.updateFormColumns()
          })
        }
      })
    },
    onLoadStart() {
      // 获取产品组分类字段
      getListByType(99).then(res => {
        const resData = res.data
        if (resData.code == 200) {
          for (const item of resData.data) {
            this.option.column[1].dicData.push({
              label: item.name,
              value: item.id,
              code: item.code,
            })
          }
        }
      })
      // 获取产品组标签list
      getTagList().then(res => {
        const resData = res.data
        if (resData.code == 200) {
          for (const item of resData.data) {
            this.option.column[13].dicData.push({
              label: item.name,
              value: item.id,
            })
          }
        }
      })
    },
    uploadBeforeIcon(file, done, loading) {
      var first = file.name.lastIndexOf('.')
      const type = file.name.substring(first + 1, file.length)
      //如果你想修改file文件,由于上传的file是只读文件，必须复制新的file才可以修改名字，完后赋值到done函数里,如果不修改的话直接写done()即可
      if (['jpg', 'jpeg', 'png'].includes(type)) {
        const isLt20M = file.size / 1024 > 100
        if (isLt20M) {
          loading()
          this.$message.error('文件大小不能超过100KB')
          return
        }
        done()
      } else {
        loading()
        this.$message.error('文件格式错误')
        return
      }
    },
    uploadBeforeImg(file, done, loading) {
      var first = file.name.lastIndexOf('.')
      const type = file.name.substring(first + 1, file.length)
      //如果你想修改file文件,由于上传的file是只读文件，必须复制新的file才可以修改名字，完后赋值到done函数里,如果不修改的话直接写done()即可
      if (['jpg', 'jpeg', 'png'].includes(type)) {
        const isLt20M = file.size / 1024 > 2000
        if (isLt20M) {
          loading()
          this.$message.error('文件大小不能超过2MB')
          return
        }
        done()
      } else {
        loading()
        this.$message.error('文件格式错误')
        return
      }
    },
    uploadAfterImg(res, done) {
      done()
      setTimeout(() => {
        this.$refs.avueFI.validateField('backgroundImg')
      }, 300)
    },
    triggerBrother(argVal) {
      this.$emit('processOnload', argVal)
    },
  },
}
</script>

<style lang="scss" scoped>
.guarantee {
  .titleTop {
    width: 319px;
    height: 24px;
    color: rgba(16, 16, 16, 100);
    font-size: 16px;
    text-align: left;
    font-family: SourceHanSansSC-bold;

    &::before {
      content: '';
      display: inline-block;
      width: 8px;
      height: 16px;
      line-height: 20px;
      border-radius: 15px;
      background-color: rgba(18, 119, 255, 100);
      text-align: center;
      transform: translateY(2px);
      box-sizing: border-box;
      margin-right: 4px;
    }
  }
  .guaranteeSetTop {
    border: 2px solid RGB(245, 245, 245);
    border-radius: 10px;
    padding-top: 40px;
    box-sizing: border-box;
  }
  .titleBottom {
    width: 319px;
    height: 24px;
    margin-top: 19px;
    color: rgba(16, 16, 16, 100);
    font-size: 16px;
    text-align: left;
    font-family: SourceHanSansSC-bold;

    &::before {
      content: '';
      display: inline-block;
      width: 8px;
      height: 16px;
      line-height: 20px;
      border-radius: 15px;
      background-color: rgba(18, 119, 255, 100);
      text-align: center;
      transform: translateY(2px);
      box-sizing: border-box;
      margin-right: 4px;
    }
  }
  .title-bottom {
    ::v-deep .avue-upload__icon {
      width: 35px;
      height: 35px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    ::v-deep .avue-upload__avatar {
      width: 35px;
      height: 35px;
    }
    ::v-deep i.el-icon-zoom-in {
      display: none;
    }
    ::v-deep .el-table td div {
      display: flex;
      justify-content: center;
      align-items: center;

      & > .el-form-item__content:last-child {
        width: 100%;
      }
    }
    ::v-deep .answer-input {
      width: 575px;
    }
  }
  ::v-deep .el-input.is-disabled .el-input__inner {
    color: #4c4b4b !important;
  }
  ::v-deep .el-tag.el-tag--info {
    color: #4c4b4b !important;
  }
  ::v-deep .el-textarea.is-disabled .el-textarea__inner {
    color: #4c4b4b !important;
  }
}
</style>
